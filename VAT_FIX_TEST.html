<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sửa Lỗi Thuế GTGT - <PERSON>hu <PERSON><PERSON>ền <PERSON>ch <PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            margin-top: 15px;
        }
        .debug {
            background: #fff3cd;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Sửa Lỗi Thuế GTGT - Thu Tiền Dịch Vụ</h1>
        <p><strong>Mục đích:</strong> Kiểm tra xem thuế suất có hiển thị đúng từ bước 2 sang bước 3 không.</p>

        <div class="test-section">
            <h3>📋 Bước 1: Thông tin cơ bản</h3>
            <div class="form-group">
                <label>Mô tả giao dịch</label>
                <input type="text" id="description" value="Thu tiền tư vấn thiết kế website" placeholder="Nhập mô tả">
            </div>
            <div class="form-group">
                <label>Số tiền (VND)</label>
                <input type="number" id="amount" value="5000000" placeholder="0">
            </div>
            <div class="form-group">
                <label>Ngày giao dịch</label>
                <input type="date" id="date" value="2024-12-19">
            </div>
        </div>

        <div class="test-section">
            <h3>💼 Bước 2: Thông tin dịch vụ</h3>
            <div class="form-group">
                <label>Tên khách hàng</label>
                <input type="text" id="customerName" value="Công ty ABC" placeholder="Tên công ty hoặc cá nhân">
            </div>
            <div class="form-group">
                <label>Loại dịch vụ</label>
                <select id="serviceType">
                    <option value="">Chọn loại dịch vụ</option>
                    <option value="consulting" selected>Tư vấn</option>
                    <option value="design">Thiết kế</option>
                    <option value="development">Phát triển</option>
                    <option value="other">Khác</option>
                </select>
            </div>
            <div class="form-group">
                <label>Thuế suất GTGT</label>
                <select id="vatRate" onchange="updateVATDisplay()">
                    <option value="5" selected>5%</option>
                    <option value="10">10%</option>
                    <option value="0">0%</option>
                </select>
                <small style="color: #666; font-size: 0.85em;">Mặc định: 5%</small>
            </div>
            <button class="btn" onclick="testStep2()">🔍 Test Bước 2</button>
        </div>

        <div class="test-section">
            <h3>✅ Bước 3: Xác nhận thông tin</h3>
            <div id="reviewContent">
                <p style="color: #666;">Nhấn "Test Bước 3" để xem kết quả...</p>
            </div>
            <button class="btn" onclick="testStep3()">🔍 Test Bước 3</button>
        </div>

        <div class="test-section">
            <h3>🐛 Debug Information</h3>
            <div id="debugInfo" class="debug">
                Chưa có thông tin debug...
            </div>
        </div>
    </div>

    <script>
        // Mô phỏng template configuration từ app chính
        const template = {
            id: 'service_revenue',
            title: 'Thu cung cấp dịch vụ',
            type: 'income',
            category: 'Dịch vụ',
            hasVAT: true,
            defaultVATRate: 5,
            vatOptions: [5, 10, 0],
            vatNote: 'Áp dụng phương pháp tính thuế GTGT trực tiếp'
        };

        let collectedData = {};

        function updateVATDisplay() {
            const vatRate = document.getElementById('vatRate').value;
            console.log('🔄 VAT Rate changed to:', vatRate);
            
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `VAT Rate thay đổi thành: ${vatRate}%<br>Thời gian: ${new Date().toLocaleTimeString()}`;
        }

        function testStep2() {
            // Mô phỏng collectStep2Data()
            collectedData = {
                description: document.getElementById('description').value.trim(),
                amount: parseFloat(document.getElementById('amount').value),
                date: document.getElementById('date').value,
                customerName: document.getElementById('customerName').value.trim(),
                serviceType: document.getElementById('serviceType').value,
                type: 'income',
                category: 'Dịch vụ'
            };

            // VAT handling
            if (template.hasVAT) {
                const vatRateEl = document.getElementById('vatRate');
                if (vatRateEl) {
                    collectedData.vatRate = parseFloat(vatRateEl.value);
                    console.log('✅ Collected VAT Rate:', collectedData.vatRate);
                }
            }

            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <strong>🔄 Dữ liệu thu thập từ Bước 2:</strong><br>
                ${JSON.stringify(collectedData, null, 2)}
            `;
            debugInfo.className = 'debug success';
        }

        function testStep3() {
            if (!collectedData.description) {
                alert('Vui lòng test Bước 2 trước!');
                return;
            }

            // Mô phỏng generateWizardReview()
            let additionalInfo = '';
            if (collectedData.customerName) additionalInfo += `<div><strong>Khách hàng:</strong> ${collectedData.customerName}</div>`;
            if (collectedData.serviceType) additionalInfo += `<div><strong>Loại dịch vụ:</strong> ${collectedData.serviceType}</div>`;
            
            // VAT information display
            if (template.hasVAT && collectedData.vatRate !== undefined && collectedData.vatRate !== null) {
                additionalInfo += `<div><strong>Thuế GTGT:</strong> ${collectedData.vatRate}%</div>`;
                console.log('✅ Displaying VAT Rate:', collectedData.vatRate + '%');
            } else if (template.hasVAT && (collectedData.vatRate === undefined || collectedData.vatRate === null)) {
                additionalInfo += `<div><strong>Thuế GTGT:</strong> ${template.defaultVATRate}% <em style="color: #ff6b6b;">(mặc định)</em></div>`;
            }

            const reviewContent = `
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
                    <h4>📋 Thông tin giao dịch</h4>
                    <div style="margin-top: 1rem; line-height: 1.6;">
                        <div><strong>Loại:</strong> ${template.title}</div>
                        <div><strong>Mô tả:</strong> ${collectedData.description}</div>
                        <div><strong>Số tiền:</strong> ${formatCurrency(collectedData.amount)}</div>
                        <div><strong>Ngày:</strong> ${formatDate(collectedData.date)}</div>
                        <div><strong>Danh mục:</strong> ${template.category}</div>
                        ${additionalInfo}
                    </div>
                </div>
            `;

            document.getElementById('reviewContent').innerHTML = reviewContent;

            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <strong>✅ Kết quả Bước 3:</strong><br>
                Template hasVAT: ${template.hasVAT}<br>
                Data vatRate: ${collectedData.vatRate}<br>
                Hiển thị thuế: ${collectedData.vatRate}%<br>
                <strong>Status:</strong> ${collectedData.vatRate === 5 ? '✅ ĐÚNG' : '❌ SAI'}
            `;
            debugInfo.className = collectedData.vatRate === 5 ? 'debug success' : 'debug error';
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('vi-VN');
        }

        // Initialize
        updateVATDisplay();
    </script>
</body>
</html>
